#!/bin/bash

# SuperMCP Development Environment Reset Script
# This script completely resets the development environment to a fresh state

set -e  # Exit on any error

echo "🧹 SuperMCP Development Environment Reset"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Step 1: Stop all services
print_status "Stopping all Docker services..."
docker-compose down --remove-orphans

# Step 2: Remove all containers, volumes, and networks
print_status "Removing all containers, volumes, and networks..."
docker-compose down --volumes --remove-orphans

# Step 3: Remove any dangling images (optional)
print_status "Cleaning up Docker images..."
docker image prune -f

# Step 4: Start services
print_status "Starting fresh Docker services..."
docker-compose up -d

# Step 5: Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 10

# Check if database is ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T db pg_isready -U postgres -d supermcp > /dev/null 2>&1; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Step 6: Run database migrations
print_status "Running database migrations..."
docker-compose exec -T backend alembic upgrade head

# Step 7: Enhanced MCP System Ready
print_status "Enhanced MCP system with user-driven recommendations ready..."
print_success "✅ Strategic MCP recommendations with marketplace intelligence"
print_success "✅ Real-time integration analysis and MCP alternatives"
print_success "✅ User-driven MCP suggestions based on custom prompts"
print_success "✅ Claude 3.5 Sonnet powered analysis for superior code generation"
print_success "✅ Complete workflow guidance from selection to deployment"

# Step 8: Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 5

# Step 9: Verify enhanced features configuration
print_status "Verifying enhanced MCP features configuration..."

# Check if Claude API is configured
if docker-compose exec -T backend python -c "import os; exit(0 if os.getenv('ANTHROPIC_API_KEY') else 1)" 2>/dev/null; then
    print_success "Claude 3.5 Sonnet API configured"
else
    print_warning "Claude API not configured - will fallback to OpenAI"
fi

# Check if Tavily API is configured for web search
if docker-compose exec -T backend python -c "import os; exit(0 if os.getenv('TAVILY_API_KEY') else 1)" 2>/dev/null; then
    print_success "Tavily API configured for marketplace search"
else
    print_warning "Tavily API not configured - limited marketplace search"
fi

# Step 10: Final status check
print_status "Checking service status..."
sleep 5

# Check if all services are running
SERVICES=("db" "redis" "backend" "celery-worker" "frontend")
ALL_HEALTHY=true

for service in "${SERVICES[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
        ALL_HEALTHY=false
    fi
done

# Final summary
echo ""
echo "🎉 Development Environment Reset Complete!"
echo "=========================================="

if [ "$ALL_HEALTHY" = true ]; then
    print_success "All services are running successfully"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Visit: http://localhost:3000"
    echo "2. Sign up/login with your GitHub account"
    echo "3. Start analyzing repositories!"
    echo ""
    echo "🚀 Enhanced Features Available:"
    echo "   ✅ Comprehensive Codebase Overview"
    echo "   ✅ Strategic MCP Recommendations with Marketplace Intelligence"
    echo "   ✅ Integration Recommendations with Real MCP Servers"
    echo "   ✅ User-Driven MCP Suggestions (Ask for Custom Recommendations)"
    echo "   ✅ Complete Workflow Selection and Generation"
    echo "   ✅ Claude 3.5 Sonnet Powered Analysis"
    echo ""
    echo "🔗 Available URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo ""
    echo "🎯 Key Improvements:"
    echo "   • Marketplace-informed MCP server recommendations"
    echo "   • Real integration analysis with replacement suggestions"
    echo "   • User-driven suggestions: Ask AI for custom MCP recommendations"
    echo "   • Strategic workflow guidance (Quick Wins → Custom Development)"
    echo "   • Enhanced codebase understanding and business logic analysis"
    echo "   • Real business logic extraction for meaningful MCP generation"
else
    print_error "Some services failed to start. Check the logs:"
    echo "   docker-compose logs"
fi

echo ""
echo "🔧 Environment Configuration:"
echo "=========================================="
echo "For optimal performance, ensure these environment variables are set:"
echo ""
echo "Required:"
echo "  GITHUB_CLIENT_ID=your_github_client_id"
echo "  GITHUB_CLIENT_SECRET=your_github_client_secret"
echo "  OPENAI_API_KEY=your_openai_api_key"
echo ""
echo "Enhanced Features (Recommended):"
echo "  ANTHROPIC_API_KEY=your_claude_api_key    # For superior MCP generation"
echo "  TAVILY_API_KEY=your_tavily_api_key       # For marketplace search"
echo "  CONTEXT7_MCP_URL=your_context7_url       # For documentation access"
echo ""
echo "💡 With all APIs configured, you get:"
echo "   • Claude 3.5 Sonnet for superior code generation"
echo "   • Real-time marketplace search for existing MCP servers"
echo "   • Enhanced documentation and integration analysis"
echo "   • AI-powered user-driven MCP recommendations"
echo ""
echo "🎤 New User-Driven Features:"
echo "   • Ask for custom MCP recommendations based on your needs"
echo "   • AI analyzes your prompt against indexed codebase"
echo "   • Get personalized suggestions linked to your actual code"
echo "   • Example: 'I want AI to help me test my API endpoints automatically'"
