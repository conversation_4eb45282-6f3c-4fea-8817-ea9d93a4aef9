'use client';

import IndexingStatus from '@/components/analysis/IndexingStatus';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/use-auth';
import apiClient from '@/lib/api';
import {
    ArrowDownTrayIcon,
    ArrowLeftIcon,
    BoltIcon,
    ChartBarIcon,
    CheckCircleIcon,
    ClockIcon,
    Cog6ToothIcon,
    CommandLineIcon,
    CpuChipIcon,
    DocumentIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    FolderIcon,
    RocketLaunchIcon,
    ServerIcon,
    ShieldCheckIcon,
    SparklesIcon,
    UserIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface MCPTool {
  tool_name: string;
  description: string;
  business_value: string;
  complexity_level: 'low' | 'medium' | 'high';
  input_schema: Record<string, any>;
  output_schema: Record<string, any>;
  implementation_hints: string;
  use_cases: string[];
  dependencies: string[];
  error_scenarios: string[];
  estimated_effort_hours?: number;
}

interface MCPSuggestions {
  // Legacy structure for backward compatibility
  categories?: Record<string, MCPTool[]>;
  prioritized_recommendations?: Array<{
    priority: number;
    tool_name: string;
    justification: string;
    estimated_impact: 'high' | 'medium' | 'low';
    business_value_score: number;
  }>;
  implementation_roadmap?: {
    phase_1_tools: string[];
    phase_2_tools: string[];
    phase_3_tools: string[];
  };

  // New strategic structure
  codebase_opportunities?: {
    data_sources: Array<{
      source_type: string;
      description: string;
      current_access_method: string;
      mcp_opportunity: string;
    }>;
    business_workflows: Array<{
      workflow_name: string;
      current_state: string;
      pain_points: string[];
      mcp_enhancement: string;
    }>;
    integration_points: Array<{
      integration_name: string;
      current_usage: string;
      automation_potential: string;
    }>;
  };
  marketplace_recommendations?: {
    quick_wins: Array<{
      server_name: string;
      marketplace_url: string;
      use_case: string;
      implementation_effort: string;
      business_impact: string;
    }>;
    existing_solutions: Array<{
      category: string;
      available_servers: string[];
      recommendation: string;
    }>;
  };
  strategic_recommendations?: {
    immediate_wins: Array<{
      tool_name: string;
      type: string;
      current_state: string;
      mcp_enhancement: string;
      business_impact: string;
      implementation_effort: string;
      success_metrics: string[];
    }>;
    custom_development: Array<{
      tool_name: string;
      business_value: string;
      current_state: string;
      mcp_enhancement: string;
      implementation_effort: string;
      technical_requirements: string[];
    }>;
  };
  integration_architecture?: {
    server_interactions: string;
    data_flow: string;
    orchestration: string;
  };
  success_metrics?: Array<{
    metric: string;
    baseline: string;
    target: string;
    measurement: string;
  }>;

  total_tools_suggested: number;
  repository_analysis?: {
    complexity_score: number;
    primary_domain: string;
    key_capabilities: string[];
    technical_stack: string[];
  };
  tool_generation_strategy?: {
    total_tools_recommended: number;
    tool_distribution: Record<string, number>;
  };
}

interface SuggestionsData {
  analysis_id: number;
  suggestions_available: boolean;
  mcp_suggestions: MCPSuggestions;
  business_analysis: Record<string, any>;
  confidence_score: number;
  implementation_complexity: Record<string, any>;
  detected_integrations?: Array<{
    integration_type: string;
    service_name: string;
    detection_method: string;
    confidence: number;
    file_locations: string[];
    package_names: string[];
    code_patterns: string[];
    env_variables: string[];
    migration_complexity: string;
    description: string;
  }>;
  mcp_alternatives?: {
    existing_mcp_servers?: Array<{
      integration: any;
      mcp_server: {
        server_name: string;
        marketplace_url: string;
        description: string;
        capabilities: string[];
        implementation_effort: string;
      };
      replacement_type: string;
      migration_effort: string;
      benefits: string[];
    }>;
    custom_development_needed?: Array<{
      integration: any;
      custom_server_needed: {
        server_name: string;
        description: string;
        estimated_effort: string;
        technical_requirements: string[];
      };
    }>;
    integration_opportunities?: Array<{
      current_integration: string;
      integration_type: string;
      automation_potential: string[];
      ai_enhancement_opportunities: string[];
    }>;
  };
}

const categoryIcons: Record<string, any> = {
  // New comprehensive categories
  CORE_BUSINESS_TOOLS: CpuChipIcon,
  WORKFLOW_AUTOMATION: SparklesIcon,
  DATA_INTEGRATION: ServerIcon,
  ANALYTICS_INTELLIGENCE: ChartBarIcon,
  COMPLIANCE_SECURITY: ShieldCheckIcon,
  OPTIMIZATION_PERFORMANCE: BoltIcon,
  USER_EXPERIENCE: UserIcon,
  // Legacy categories (for backward compatibility)
  CORE_OPERATIONS: CpuChipIcon,
  DATA_MANAGEMENT: ServerIcon,
  INTEGRATION: Cog6ToothIcon,
  UTILITIES: CommandLineIcon,
  MONITORING: ChartBarIcon,
  AUTOMATION: SparklesIcon,
  REPORTING: DocumentTextIcon
};

const categoryColors: Record<string, string> = {
  CORE_OPERATIONS: 'bg-blue-100 text-blue-800',
  DATA_MANAGEMENT: 'bg-green-100 text-green-800',
  INTEGRATION: 'bg-purple-100 text-purple-800',
  UTILITIES: 'bg-orange-100 text-orange-800',
  MONITORING: 'bg-red-100 text-red-800',
  AUTOMATION: 'bg-yellow-100 text-yellow-800',
  REPORTING: 'bg-indigo-100 text-indigo-800'
};

export default function MCPSuggestionsPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const analysisId = parseInt(params.id as string);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const [suggestionsData, setSuggestionsData] = useState<SuggestionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTools, setSelectedTools] = useState<Array<{tool_name: string, description: string, category: string}>>([]);
  const [targetLanguage, setTargetLanguage] = useState('python');
  const [hostingArchitecture, setHostingArchitecture] = useState('http-sse');
  const [workflowStrategy, setWorkflowStrategy] = useState('quick_wins');
  const [generationInProgress, setGenerationInProgress] = useState(false);
  const [downloadInProgress, setDownloadInProgress] = useState(false);
  const [generatedZipPath, setGeneratedZipPath] = useState<string | null>(null);
  const [languageRecommendation, setLanguageRecommendation] = useState<any>(null);
  const [generatedProjectStructure, setGeneratedProjectStructure] = useState<string[] | null>(null);
  const [setupInstructions, setSetupInstructions] = useState<string>('');
  const [activeTab, setActiveTab] = useState('overview');
  const [error, setError] = useState<string | null>(null);
  const [hasTriggeredEnhancement, setHasTriggeredEnhancement] = useState(false);
  const [pollCount, setPollCount] = useState(0);
  const [isRegenerating, setIsRegenerating] = useState(false);

  // User-driven suggestions state
  const [userPrompt, setUserPrompt] = useState('');
  const [userSuggestions, setUserSuggestions] = useState<any>(null);
  const [userSuggestionsLoading, setUserSuggestionsLoading] = useState(false);

  useEffect(() => {
    fetchSuggestions();
  }, [analysisId]);

  // Auto-trigger enhanced suggestions generation if not available (one time only)
  useEffect(() => {
    if (suggestionsData && !loading && !hasTriggeredEnhancement && suggestionsData.suggestions_available) {
      const hasEnhancedStructure = suggestionsData.mcp_suggestions.categories && 
        Object.keys(suggestionsData.mcp_suggestions.categories).some(cat => 
          ['CORE_OPERATIONS', 'DATA_MANAGEMENT', 'INTEGRATION', 'UTILITIES', 'MONITORING', 'AUTOMATION', 'REPORTING'].includes(cat)
        );
      
      const hasAnyTools = suggestionsData.mcp_suggestions.total_tools_suggested > 0;
      
      // Only trigger if we have suggestions available but they're empty or not enhanced
      if (!hasEnhancedStructure && !hasAnyTools && !isRegenerating) {
        console.log('Triggering enhanced suggestions generation once...');
        setHasTriggeredEnhancement(true);
        setIsRegenerating(true);
        fetchSuggestions(true);
      }
    }
  }, [suggestionsData, loading, hasTriggeredEnhancement]);

  const fetchSuggestions = async (regenerate = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Fetching MCP suggestions for analysis:', analysisId, 'regenerate:', regenerate);
      const data = await apiClient.getMCPSuggestions(analysisId, regenerate);

      console.log('📦 MCP Suggestions Response:', data);
      console.log('📂 Categories found:', Object.keys(data.mcp_suggestions?.categories || {}));
      console.log('🔢 Total tools per category:', Object.entries(data.mcp_suggestions?.categories || {}).map(([cat, tools]) => [cat, Array.isArray(tools) ? tools.length : 0]));
      console.log('✅ Suggestions available:', data.suggestions_available);
      console.log('📊 Total tools suggested:', data.mcp_suggestions?.total_tools_suggested);
      console.log('🔗 Detected integrations:', data.detected_integrations?.length || 0);
      console.log('🔄 MCP alternatives:', data.mcp_alternatives);
      console.log('🟢 Existing MCP servers:', data.mcp_alternatives?.existing_mcp_servers?.length || 0);
      console.log('🟣 Custom development needed:', data.mcp_alternatives?.custom_development_needed?.length || 0);

      if (data.suggestions_available) {
        setSuggestionsData(data);
        setPollCount(0); // Reset poll count on success
        setIsRegenerating(false); // Reset regeneration flag

        // Fetch language recommendation
        try {
          const langResponse = await apiClient.get(`/mcp-generation/analysis/${analysisId}/language-recommendation`);
          setLanguageRecommendation(langResponse.data);

          // Auto-set recommended language if not already set by user
          if (langResponse.data.recommended_language && targetLanguage === 'python') {
            setTargetLanguage(langResponse.data.recommended_language);
          }
        } catch (langError) {
          console.warn('Could not fetch language recommendation:', langError);
          // Continue with default language
        }
      } else if (pollCount < 12) { // Max 12 polls = 1 minute
        // Suggestions are being generated, poll for updates (without regenerate flag)
        console.log(`Suggestions not ready, will poll again in 5 seconds... (attempt ${pollCount + 1}/12)`);
        setPollCount(prev => prev + 1);
        setTimeout(() => fetchSuggestions(false), 5000); // Always poll without regenerate
      } else {
        console.log('Max polling attempts reached. Suggestions generation may have failed.');
        setError('Suggestion generation is taking longer than expected. Please refresh the page to try again.');
      }
    } catch (err: any) {
      console.error('❌ Failed to fetch MCP suggestions:', err);
      console.error('📋 Error details:', {
        status: err.response?.status,
        statusText: err.response?.statusText,
        data: err.response?.data,
        message: err.message
      });

      // Handle specific error cases
      if (err.response?.status === 401 || err.response?.status === 403) {
        // Authentication error - redirect to login
        console.log('🔐 Authentication required, redirecting to login...');
        router.push('/auth/login');
        return;
      } else if (err.response?.status === 400) {
        // Parse specific error messages
        const errorDetail = err.response?.data?.detail || err.message || 'Unknown error';

        if (errorDetail.includes('quota exceeded') || errorDetail.includes('AI_QUOTA_EXCEEDED')) {
          setError('AI service quota exceeded. Please check your billing and try again later.');
        } else if (errorDetail.includes('rate limit') || errorDetail.includes('AI_RATE_LIMITED')) {
          setError('AI service rate limited. Please wait a few minutes and try again.');
        } else if (errorDetail.includes('authentication') || errorDetail.includes('AI_AUTH_ERROR')) {
          setError('AI service authentication failed. Please check your API key configuration.');
        } else if (errorDetail.includes('model') || errorDetail.includes('AI_MODEL_ERROR')) {
          setError('AI model not available. Please try again later.');
        } else if (regenerate) {
          console.log('Regeneration request failed, will continue polling for existing task...');
          setIsRegenerating(false);
          // Continue polling without regenerate
          setTimeout(() => fetchSuggestions(false), 5000);
          return;
        } else {
          setError(`Failed to load MCP suggestions: ${errorDetail}`);
        }
      } else {
        setError('Failed to load MCP suggestions');
      }
      setIsRegenerating(false);
    } finally {
      setLoading(false);
    }
  };

  const handleToolSelection = (tool: any, selected: boolean) => {
    if (selected) {
      setSelectedTools([...selectedTools, {
        tool_name: tool.tool_name,
        description: tool.description || tool.business_value || 'AI-generated MCP tool',
        category: 'LEGACY_TOOL'
      }]);
    } else {
      setSelectedTools(selectedTools.filter(t => t.tool_name !== tool.tool_name));
    }
  };

  const generateMCPServer = async () => {
    if (selectedTools.length === 0) return;

    setGenerationInProgress(true);
    setError(null);
    setGeneratedZipPath(null);
    setGeneratedProjectStructure(null);
    setSetupInstructions('');

    try {
      const response = await apiClient.generateMCPServer({
        analysis_id: analysisId,
        selected_tools: selectedTools,
        target_language: targetLanguage,
        customization_options: {
          include_tests: true,
          include_docker: true,
          authentication_method: 'api_key',
          hosting_type: hostingArchitecture
        }
      });

      if (response.success) {
        const zipPath = response.generation_result.zip_file_path;
        const projectStructure = response.generation_result.project_structure || [];
        const setupInstructions = response.generation_result.setup_instructions || '';

        setGeneratedZipPath(zipPath);
        setGeneratedProjectStructure(projectStructure);
        setSetupInstructions(setupInstructions);
      }
    } catch (err: any) {
      console.error('Failed to generate MCP server:', err);
      
      // Handle different types of errors
      if (err.code === 'ECONNABORTED') {
        setError('Generation is taking longer than expected. This is normal for complex repositories. Please wait...');
        // Optionally implement polling here
      } else if (err.response?.status === 500) {
        setError('Server error during generation. Please try again or contact support.');
      } else {
        setError(`Failed to generate MCP server: ${err.message || 'Unknown error'}`);
      }
    } finally {
      setGenerationInProgress(false);
    }
  };

  const generateUserSuggestions = async () => {
    if (!userPrompt.trim()) return;

    setUserSuggestionsLoading(true);
    setError(null);

    try {
      const data = await apiClient.getUserMCPSuggestions({
        analysis_id: parseInt(analysisId),
        user_prompt: userPrompt,
        focus_areas: []
      });

      setUserSuggestions(data.suggestions);
    } catch (err: any) {
      console.error('Failed to generate user suggestions:', err);
      setError(`Failed to generate suggestions: ${err.message || 'Unknown error'}`);
    } finally {
      setUserSuggestionsLoading(false);
    }
  };

  const downloadMCPServer = async () => {
    if (!generatedZipPath) return;

    setDownloadInProgress(true);
    try {
      const blob = await apiClient.downloadMCPServer(analysisId, generatedZipPath);
      
      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mcp-server-${Date.now()}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Failed to download MCP server:', err);
      setError(`Failed to download MCP server: ${err.message || 'Unknown error'}`);
    } finally {
      setDownloadInProgress(false);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSelectedToolsCount = () => {
    return selectedTools.length;
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Checking authentication...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="bg-white border border-gray-200 rounded-lg p-12">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-gray-600">Generating AI-powered MCP tool suggestions...</p>
              <p className="mt-2 text-sm text-gray-500">This may take a few moments while we analyze your repository</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!suggestionsData) {
    return (
      <DashboardLayout>
        <div className="bg-white border border-gray-200 rounded-lg p-12">
          <div className="text-center">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No suggestions available</h3>
            <p className="mt-2 text-gray-500">
              MCP suggestions could not be generated for this analysis.
            </p>
            <div className="mt-6">
              <Button onClick={() => router.back()} variant="outline">
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back to Analysis
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }


  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-4"
          >
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Analysis
          </button>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">MCP Tool Recommendations</h1>
              <p className="text-gray-600">
                AI-generated Model Context Protocol (MCP) tools for your repository
              </p>
            </div>
            <div className="text-center">
              <div className="bg-primary/10 border border-primary/20 rounded-lg px-4 py-3">
                <p className="text-2xl font-bold text-primary mb-1">
                  {suggestionsData.mcp_suggestions.total_tools_suggested}
                </p>
                <p className="text-sm text-primary">Tools Suggested</p>
              </div>
            </div>
          </div>
        </div>

        {/* Indexing Status */}
        <IndexingStatus
          analysisId={analysisId}
          analysisStatus="completed"
          autoRefresh={false}
        />



        {/* Main Content */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="border-b border-gray-200">
              <TabsList className="h-12 w-full bg-transparent p-0 justify-start rounded-none">
                <TabsTrigger
                  value="overview"
                  className="h-12 px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none"
                >
                  <ChartBarIcon className="h-4 w-4 mr-2" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="tools"
                  className="h-12 px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none"
                >
                  <CpuChipIcon className="h-4 w-4 mr-2" />
                  Tool Selection
                </TabsTrigger>
                <TabsTrigger
                  value="integrations"
                  className="h-12 px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none"
                >
                  <BoltIcon className="h-4 w-4 mr-2" />
                  Integration Recommendations
                </TabsTrigger>
                <TabsTrigger
                  value="user-suggestions"
                  className="h-12 px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none"
                >
                  <SparklesIcon className="h-4 w-4 mr-2" />
                  Ask for Suggestions
                </TabsTrigger>
                <TabsTrigger
                  value="generate"
                  className="h-12 px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none"
                >
                  <RocketLaunchIcon className="h-4 w-4 mr-2" />
                  Generate Server
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="overview" className="p-6 space-y-6">
              {/* Codebase Understanding Header */}
              <Card className="border border-blue-200 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl font-semibold text-blue-900">
                    <CheckCircleIcon className="h-6 w-6 mr-3 text-blue-600" />
                    ✅ We Understand Your Complete Codebase
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Our AI has analyzed your repository structure, business logic, workflows, and integration patterns
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Repository Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <FolderIcon className="h-5 w-5 mr-3 text-primary" />
                      Repository Profile
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Primary Domain</h4>
                      <div className="bg-primary/10 border border-primary/20 rounded-lg p-3">
                        <p className="text-primary font-medium">
                          {suggestionsData.business_analysis.business_logic?.primary_domain || 'Repository Analysis'}
                        </p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Business Purpose</h4>
                      <div className="bg-green-50 border border-green-100 rounded-lg p-3">
                        <p className="text-green-800 text-sm leading-relaxed">
                          {suggestionsData.business_analysis.business_logic?.business_purpose ||
                           'Comprehensive repository analysis with AI-powered MCP tool generation'}
                        </p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Value Proposition</h4>
                      <div className="bg-purple-50 border border-purple-100 rounded-lg p-3">
                        <p className="text-purple-800 text-sm leading-relaxed">
                          {suggestionsData.business_analysis.business_logic?.value_proposition ||
                           'Enables AI-powered automation and intelligent assistance'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <CpuChipIcon className="h-5 w-5 mr-3 text-primary" />
                      Technical Architecture
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Core Operations</h4>
                      <div className="space-y-2">
                        {(suggestionsData.business_analysis.business_logic?.core_operations || []).slice(0, 3).map((operation: any, index: number) => (
                          <div key={index} className="bg-gray-50 border border-gray-100 rounded-lg p-2">
                            <p className="text-gray-700 text-sm">
                              {typeof operation === 'string' ? operation : operation.name || operation}
                            </p>
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.business_logic?.core_operations || []).length === 0 && (
                          <div className="bg-gray-50 border border-gray-100 rounded-lg p-2">
                            <p className="text-gray-500 text-sm">Analyzing core operations...</p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Business Entities</h4>
                      <div className="flex flex-wrap gap-2">
                        {(suggestionsData.business_analysis.business_logic?.business_entities || []).slice(0, 4).map((entity: string, index: number) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {entity}
                          </Badge>
                        ))}
                        {(suggestionsData.business_analysis.business_logic?.business_entities || []).length === 0 && (
                          <Badge variant="outline" className="text-xs">
                            Analyzing entities...
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* API Capabilities & Workflows */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <ServerIcon className="h-5 w-5 mr-3 text-primary" />
                      API Capabilities
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Existing APIs</h4>
                      <div className="space-y-2">
                        {(suggestionsData.business_analysis.api_capabilities?.existing_apis || []).slice(0, 3).map((api: any, index: number) => (
                          <div key={index} className="bg-blue-50 border border-blue-100 rounded-lg p-2">
                            <div className="flex items-center justify-between">
                              <p className="text-blue-800 text-sm font-medium">
                                {typeof api === 'string' ? api : api.endpoint || api.name || 'API Endpoint'}
                              </p>
                              <Badge variant="outline" className="text-xs">
                                {typeof api === 'object' ? api.method || 'GET' : 'API'}
                              </Badge>
                            </div>
                            {typeof api === 'object' && api.purpose && (
                              <p className="text-blue-600 text-xs mt-1">{api.purpose}</p>
                            )}
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.api_capabilities?.existing_apis || []).length === 0 && (
                          <div className="bg-gray-50 border border-gray-100 rounded-lg p-2">
                            <p className="text-gray-500 text-sm">No APIs detected or analyzing...</p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">External Integrations</h4>
                      <div className="flex flex-wrap gap-2">
                        {(suggestionsData.business_analysis.api_capabilities?.external_integrations || []).slice(0, 4).map((integration: string, index: number) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {integration}
                          </Badge>
                        ))}
                        {(suggestionsData.business_analysis.api_capabilities?.external_integrations || []).length === 0 && (
                          <Badge variant="outline" className="text-xs">
                            Analyzing integrations...
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <SparklesIcon className="h-5 w-5 mr-3 text-primary" />
                      Workflows & Automation
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Business Processes</h4>
                      <div className="space-y-2">
                        {(suggestionsData.business_analysis.workflows?.business_processes || []).slice(0, 3).map((process: any, index: number) => (
                          <div key={index} className="bg-green-50 border border-green-100 rounded-lg p-2">
                            <p className="text-green-800 text-sm font-medium">
                              {typeof process === 'string' ? process : process.process_name || process.name || process}
                            </p>
                            {typeof process === 'object' && process.automation_level && (
                              <Badge variant="outline" className="text-xs mt-1">
                                {process.automation_level}
                              </Badge>
                            )}
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.workflows?.business_processes || []).length === 0 && (
                          <div className="bg-gray-50 border border-gray-100 rounded-lg p-2">
                            <p className="text-gray-500 text-sm">Analyzing workflows...</p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Automation Opportunities</h4>
                      <div className="space-y-1">
                        {(suggestionsData.business_analysis.workflows?.automation_opportunities || []).slice(0, 3).map((opportunity: string, index: number) => (
                          <div key={index} className="bg-yellow-50 border border-yellow-100 rounded-lg p-2">
                            <p className="text-yellow-800 text-xs">{opportunity}</p>
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.workflows?.automation_opportunities || []).length === 0 && (
                          <div className="bg-gray-50 border border-gray-100 rounded-lg p-2">
                            <p className="text-gray-500 text-xs">Identifying opportunities...</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Analysis Summary Stats */}
              <Card className="border border-gray-200 shadow-sm">
                <CardHeader className="bg-gray-50 border-b border-gray-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                    <ChartBarIcon className="h-5 w-5 mr-3 text-primary" />
                    Analysis Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-primary mb-1">
                        {suggestionsData.mcp_suggestions.total_tools_suggested}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">MCP Tools</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {(suggestionsData.mcp_alternatives?.existing_mcp_servers?.length || 0) +
                         (suggestionsData.mcp_alternatives?.custom_development_needed?.length || 0) ||
                         Object.keys(suggestionsData.mcp_suggestions.categories || {}).length}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Recommendations</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-purple-600 mb-1">
                        {Math.round((suggestionsData.confidence_score || 0.75) * 100)}%
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Confidence</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {(suggestionsData.business_analysis.business_logic?.core_operations || []).length +
                         (suggestionsData.business_analysis.api_capabilities?.existing_apis || []).length}
                      </div>
                      <div className="text-sm text-gray-600 font-medium">Features Analyzed</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Repository Understanding */}
              <Card className="border border-green-200 shadow-sm">
                <CardHeader className="bg-green-50 border-b border-green-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-green-900">
                    <ExclamationTriangleIcon className="h-5 w-5 mr-3 text-green-600" />
                    What Problems Does This Code Solve?
                  </CardTitle>
                  <CardDescription className="text-green-700">
                    Understanding the key pain points and business challenges this codebase addresses
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {suggestionsData.business_analysis.business_logic?.use_cases &&
                     suggestionsData.business_analysis.business_logic.use_cases.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {suggestionsData.business_analysis.business_logic.use_cases.slice(0, 4).map((useCase: any, index: number) => (
                          <div key={index} className="bg-white border border-green-100 rounded-lg p-4">
                            <h4 className="font-medium text-green-900 mb-2">
                              {useCase.scenario || `Use Case ${index + 1}`}
                            </h4>
                            <p className="text-sm text-green-700 mb-2">
                              {useCase.user_story || useCase.description || 'Solving specific business challenges'}
                            </p>
                            <div className="text-xs text-green-600">
                              <span className="font-medium">Outcome:</span> {useCase.business_outcome || 'Improved efficiency and automation'}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="bg-green-50 border border-green-100 rounded-lg p-4">
                        <div className="space-y-3">
                          <div className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                              <h4 className="font-medium text-green-900">Automation & Efficiency</h4>
                              <p className="text-sm text-green-700">Automates manual processes and reduces human error</p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                              <h4 className="font-medium text-green-900">Scalability</h4>
                              <p className="text-sm text-green-700">Provides scalable solutions for growing business needs</p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                              <h4 className="font-medium text-green-900">Integration</h4>
                              <p className="text-sm text-green-700">Connects disparate systems and data sources</p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                            <div>
                              <h4 className="font-medium text-green-900">Cost Reduction</h4>
                              <p className="text-sm text-green-700">Improves efficiency and reduces operational costs</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Core Business Logic & API Endpoints */}
              <Card className="border border-purple-200 shadow-sm">
                <CardHeader className="bg-purple-50 border-b border-purple-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-purple-900">
                    <Cog6ToothIcon className="h-5 w-5 mr-3 text-purple-600" />
                    Core Business Logic & API Endpoints
                  </CardTitle>
                  <CardDescription className="text-purple-700">
                    Key business operations and API endpoints that drive the core functionality
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Business Logic */}
                    <div>
                      <h4 className="font-medium text-purple-900 mb-3">Core Business Operations</h4>
                      <div className="space-y-2">
                        {(suggestionsData.business_analysis.business_logic?.core_operations || []).slice(0, 5).map((operation: any, index: number) => (
                          <div key={index} className="bg-white border border-purple-100 rounded-lg p-3">
                            <div className="font-medium text-purple-800 text-sm">
                              {typeof operation === 'string' ? operation : operation.name || operation}
                            </div>
                            {typeof operation === 'object' && operation.description && (
                              <div className="text-xs text-purple-600 mt-1">{operation.description}</div>
                            )}
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.business_logic?.core_operations || []).length === 0 && (
                          <div className="bg-purple-50 border border-purple-100 rounded-lg p-3">
                            <p className="text-purple-600 text-sm">Analyzing core business operations...</p>
                          </div>
                        )}
                      </div>

                      {suggestionsData.business_analysis.business_logic?.business_entities &&
                       suggestionsData.business_analysis.business_logic.business_entities.length > 0 && (
                        <div className="mt-4">
                          <h5 className="font-medium text-purple-900 mb-2">Key Business Entities</h5>
                          <div className="flex flex-wrap gap-2">
                            {suggestionsData.business_analysis.business_logic.business_entities.slice(0, 8).map((entity: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs bg-purple-100 text-purple-800">
                                {entity}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* API Endpoints */}
                    <div>
                      <h4 className="font-medium text-purple-900 mb-3">API Endpoints Related to Core Business</h4>
                      <div className="space-y-2">
                        {(suggestionsData.business_analysis.api_capabilities?.existing_apis || []).slice(0, 5).map((api: any, index: number) => (
                          <div key={index} className="bg-white border border-purple-100 rounded-lg p-3">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-mono text-sm text-purple-800">{api.endpoint || api.path}</span>
                              <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700">
                                {api.method || 'GET'}
                              </Badge>
                            </div>
                            <div className="text-xs text-purple-600">{api.purpose || api.description || 'API endpoint'}</div>
                          </div>
                        ))}
                        {(suggestionsData.business_analysis.api_capabilities?.existing_apis || []).length === 0 && (
                          <div className="bg-purple-50 border border-purple-100 rounded-lg p-3">
                            <p className="text-purple-600 text-sm">No specific API endpoints detected in analysis</p>
                            <p className="text-purple-500 text-xs mt-1">The system may use internal APIs or endpoints not captured in current scope</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Business Rules */}
                  {suggestionsData.business_analysis.business_logic?.business_rules &&
                   suggestionsData.business_analysis.business_logic.business_rules.length > 0 && (
                    <div className="mt-6 pt-6 border-t border-purple-100">
                      <h4 className="font-medium text-purple-900 mb-3">Business Rules & Logic</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {suggestionsData.business_analysis.business_logic.business_rules.slice(0, 6).map((rule: string, index: number) => (
                          <div key={index} className="bg-purple-50 border border-purple-100 rounded-lg p-3">
                            <p className="text-sm text-purple-800">{rule}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tools" className="p-6 space-y-6">
              {/* Selection Summary */}
              <Card className="border border-gray-200 shadow-sm bg-gray-50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <CheckCircleIcon className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">Selection Summary</h3>
                        <p className="text-sm text-gray-600">Choose tools to include in your MCP server</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-primary">{selectedTools.length}</div>
                      <div className="text-sm text-gray-600">Selected Tools</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

            {/* Specific Recommendations Based on Codebase */}
            {suggestionsData.mcp_suggestions?.specific_recommendations &&
             suggestionsData.mcp_suggestions.specific_recommendations.length > 0 && (
              <Card className="border border-blue-200 shadow-sm overflow-hidden">
                <CardHeader className="bg-blue-50 border-b border-blue-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-blue-900">
                    <CpuChipIcon className="h-5 w-5 mr-3 text-blue-600" />
                    🎯 Specific MCP Recommendations for This Codebase
                    <div className="ml-auto bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {suggestionsData.mcp_suggestions.specific_recommendations.length} recommendations
                    </div>
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Tailored MCP server recommendations based on your actual codebase functionality
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {suggestionsData.mcp_suggestions.specific_recommendations.map((recommendation: any, index: number) => (
                      <div key={index} className="border border-blue-100 rounded-lg p-4 bg-blue-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-semibold text-blue-900">{recommendation.server_name}</h4>
                              <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800">
                                {recommendation.estimated_effort}
                              </Badge>
                              {recommendation.implementation_type === 'existing_server' && (
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                                  Ready to Use
                                </Badge>
                              )}
                            </div>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium text-blue-900">Description:</span>
                                <span className="text-blue-800 ml-1">{recommendation.description}</span>
                              </div>
                              <div>
                                <span className="font-medium text-blue-900">Current State:</span>
                                <span className="text-blue-800 ml-1">{recommendation.current_functionality}</span>
                              </div>
                              <div>
                                <span className="font-medium text-blue-900">MCP Enhancement:</span>
                                <span className="text-blue-800 ml-1">{recommendation.mcp_enhancement}</span>
                              </div>
                              <div>
                                <span className="font-medium text-blue-900">Business Value:</span>
                                <span className="text-blue-800 ml-1">{recommendation.business_value}</span>
                              </div>
                              {recommendation.existing_mcp_server && (
                                <div>
                                  <span className="font-medium text-blue-900">Existing Server:</span>
                                  <span className="text-blue-800 ml-1">{recommendation.existing_mcp_server}</span>
                                  {recommendation.marketplace_url && (
                                    <a
                                      href={recommendation.marketplace_url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:text-blue-800 text-xs underline ml-2"
                                    >
                                      View →
                                    </a>
                                  )}
                                </div>
                              )}
                              {recommendation.technical_requirements && recommendation.technical_requirements.length > 0 && (
                                <div>
                                  <span className="font-medium text-blue-900">Technical Requirements:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {recommendation.technical_requirements.map((req: string, idx: number) => (
                                      <Badge key={idx} variant="secondary" className="text-xs">
                                        {req}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <Checkbox
                            checked={selectedTools.some(t => t.tool_name === recommendation.server_name)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedTools([...selectedTools, {
                                  tool_name: recommendation.server_name,
                                  description: recommendation.description,
                                  category: 'SPECIFIC_RECOMMENDATION'
                                }]);
                              } else {
                                setSelectedTools(selectedTools.filter(t => t.tool_name !== recommendation.server_name));
                              }
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Strategic Recommendations */}
            {/* Quick Wins from Marketplace */}
            {suggestionsData.mcp_alternatives?.existing_mcp_servers &&
             suggestionsData.mcp_alternatives.existing_mcp_servers.length > 0 && (
              <Card className="border border-green-200 shadow-sm overflow-hidden">
                <CardHeader className="bg-green-50 border-b border-green-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-green-900">
                    <RocketLaunchIcon className="h-5 w-5 mr-3 text-green-600" />
                    🚀 Quick Wins - Ready-to-Deploy MCP Servers
                    <div className="ml-auto bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      {suggestionsData.mcp_alternatives.existing_mcp_servers.length} servers
                    </div>
                  </CardTitle>
                  <CardDescription className="text-green-700">
                    Existing MCP servers from marketplace that can be deployed immediately (1-week implementation)
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {suggestionsData.mcp_alternatives.existing_mcp_servers.map((alternative: any, index: number) => {
                      const server = alternative.mcp_server;
                      return (
                      <div key={index} className="border border-green-100 rounded-lg p-4 bg-green-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-semibold text-green-900">{server.server_name}</h4>
                              <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                                {alternative.migration_effort}
                              </Badge>
                            </div>
                            <p className="text-green-800 text-sm mb-2">{server.description}</p>
                            <p className="text-green-700 text-xs mb-2">
                              <strong>Replaces:</strong> {alternative.integration.service_name}
                            </p>
                            {server.marketplace_url && (
                              <a
                                href={server.marketplace_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-green-600 hover:text-green-800 text-xs underline"
                              >
                                View in Marketplace →
                              </a>
                            )}
                          </div>
                          <Checkbox
                            checked={selectedTools.some(tool => tool.tool_name === server.server_name)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedTools([...selectedTools, {
                                  tool_name: server.server_name,
                                  description: server.use_case,
                                  category: 'QUICK_WINS'
                                }]);
                              } else {
                                setSelectedTools(selectedTools.filter(tool => tool.tool_name !== server.server_name));
                              }
                            }}
                          />
                        </div>
                      </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Custom Development */}
            {suggestionsData.mcp_alternatives?.custom_development_needed &&
             suggestionsData.mcp_alternatives.custom_development_needed.length > 0 && (
              <Card className="border border-blue-200 shadow-sm overflow-hidden">
                <CardHeader className="bg-blue-50 border-b border-blue-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-blue-900">
                    <BoltIcon className="h-5 w-5 mr-3 text-blue-600" />
                    🛠️ Custom Development - Domain-Specific Solutions
                    <div className="ml-auto bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {suggestionsData.mcp_alternatives.custom_development_needed.length} tools
                    </div>
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Custom MCP servers tailored to your specific business logic and integrations
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {suggestionsData.mcp_alternatives.custom_development_needed.map((custom: any, index: number) => {
                      const tool = custom.custom_server_needed;
                      return (
                      <div key={index} className="border border-blue-100 rounded-lg p-4 bg-blue-50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-semibold text-blue-900">{tool.server_name}</h4>
                              <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800">
                                Custom
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {tool.estimated_effort}
                              </Badge>
                            </div>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium text-blue-900">Description:</span>
                                <span className="text-blue-800 ml-1">{tool.description}</span>
                              </div>
                              <div>
                                <span className="font-medium text-blue-900">Current Integration:</span>
                                <span className="text-blue-800 ml-1">{custom.integration.service_name}</span>
                              </div>
                              <div>
                                <span className="font-medium text-blue-900">Development Effort:</span>
                                <span className="text-blue-800 ml-1">{tool.estimated_effort}</span>
                              </div>
                              {tool.technical_requirements && tool.technical_requirements.length > 0 && (
                                <div>
                                  <span className="font-medium text-blue-900">Technical Requirements:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {tool.technical_requirements.map((req: string, idx: number) => (
                                      <Badge key={idx} variant="secondary" className="text-xs">
                                        {req}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <Checkbox
                            checked={selectedTools.some(t => t.tool_name === tool.server_name)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedTools([...selectedTools, {
                                  tool_name: tool.server_name,
                                  description: tool.description,
                                  category: 'CUSTOM_DEVELOPMENT'
                                }]);
                              } else {
                                setSelectedTools(selectedTools.filter(t => t.tool_name !== tool.server_name));
                              }
                            }}
                          />
                        </div>
                      </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}



            {/* Legacy Categories Fallback */}
            {suggestionsData.mcp_suggestions.categories && Object.entries(suggestionsData.mcp_suggestions.categories).map(([category, tools]) => {
              if (!tools || tools.length === 0) return null;

              const IconComponent = categoryIcons[category] || CpuChipIcon;
              
              return (
                <Card key={category} className="border border-gray-200 shadow-sm overflow-hidden">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <IconComponent className="h-5 w-5 mr-3 text-primary" />
                      {category.replace(/_/g, ' ')}
                      <div className="ml-auto bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                        {tools.length} tools
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid gap-4">
                      {tools.map((tool) => (
                        <div key={tool.tool_name} className="group border border-gray-200 rounded-lg p-4 hover:shadow-sm hover:border-gray-300 transition-all duration-200 bg-white">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 flex-1">
                              <Checkbox
                                checked={selectedTools.some(t => t.tool_name === tool.tool_name)}
                                onCheckedChange={(checked) =>
                                  handleToolSelection(tool, checked as boolean)
                                }
                                className="mt-1 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                              />
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 text-lg mb-2">
                                  {tool.tool_name.replace(/_/g, ' ')}
                                </h4>
                                <p className="text-gray-600 leading-relaxed mb-3">
                                  {tool.description || tool.business_value || 'AI-generated MCP tool'}
                                </p>
                                {tool.business_value && tool.business_value !== tool.description && (
                                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                                    <div className="flex items-center space-x-2 mb-1">
                                      <BoltIcon className="h-4 w-4 text-blue-600" />
                                      <span className="text-sm font-semibold text-blue-900">Business Value</span>
                                    </div>
                                    <p className="text-sm text-blue-800">{tool.business_value}</p>
                                  </div>
                                )}
                                {tool.repository_evidence && (
                                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                                    <div className="flex items-center space-x-2 mb-1">
                                      <CheckCircleIcon className="h-4 w-4 text-green-600" />
                                      <span className="text-sm font-semibold text-green-900">Repository Evidence</span>
                                    </div>
                                    <p className="text-sm text-green-800">{tool.repository_evidence}</p>
                                  </div>
                                )}
                                <div className="flex items-center space-x-4 mb-3">
                                  <Badge className={`${getComplexityColor(tool.complexity_level)} font-semibold`}>
                                    {tool.complexity_level} complexity
                                  </Badge>
                                </div>
                                {tool.use_cases && tool.use_cases.length > 0 && (
                                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
                                    <p className="text-xs font-semibold text-gray-700 mb-2">Use cases:</p>
                                    <ul className="space-y-1">
                                      {(tool.use_cases || []).slice(0, 3).map((useCase, idx) => (
                                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                                          <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mr-2 flex-shrink-0"></div>
                                          {useCase}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

            <TabsContent value="integrations" className="p-6 space-y-6">
              {/* Enhanced Integration Recommendations Header */}
              <Card className="border border-orange-200 shadow-sm bg-gradient-to-r from-orange-50 to-yellow-50">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl font-semibold text-orange-900">
                    <BoltIcon className="h-6 w-6 mr-3 text-orange-600" />
                    🔗 Smart Integration Recommendations
                  </CardTitle>
                  <CardDescription className="text-orange-700">
                    Marketplace-informed MCP alternatives to replace heavyweight integrations with AI-native solutions
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Existing MCP Servers for Detected Integrations */}
              {suggestionsData?.mcp_alternatives?.existing_mcp_servers &&
               suggestionsData.mcp_alternatives.existing_mcp_servers.length > 0 && (
                <Card className="border border-green-200 shadow-sm">
                  <CardHeader className="bg-green-50 border-b border-green-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-green-900">
                      <CheckCircleIcon className="h-5 w-5 mr-3 text-green-600" />
                      ✅ Ready-to-Use MCP Servers
                      <div className="ml-auto bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        {suggestionsData.mcp_alternatives.existing_mcp_servers.length} available
                      </div>
                    </CardTitle>
                    <CardDescription className="text-green-700">
                      Existing MCP servers from marketplace that can directly replace your current integrations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      {suggestionsData.mcp_alternatives.existing_mcp_servers.map((alternative: any, index: number) => (
                        <div key={index} className="border border-green-100 rounded-lg p-6 bg-green-50">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="text-lg font-semibold text-green-900">
                                  {alternative.mcp_server.server_name}
                                </h3>
                                <Badge variant="outline" className="bg-green-100 text-green-800">
                                  {alternative.replacement_type.replace('_', ' ')}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {alternative.migration_effort}
                                </Badge>
                              </div>
                              <p className="text-green-800 text-sm mb-3">
                                {alternative.mcp_server.description}
                              </p>

                              {/* Current Integration Details */}
                              <div className="bg-white border border-green-200 rounded-lg p-4 mb-4">
                                <h4 className="font-medium text-green-900 mb-2">Replaces: {alternative.integration.service_name}</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <span className="font-medium text-green-800">Type:</span>
                                    <span className="ml-1 text-green-700">{alternative.integration.integration_type}</span>
                                  </div>
                                  <div>
                                    <span className="font-medium text-green-800">Detection:</span>
                                    <span className="ml-1 text-green-700">{alternative.integration.detection_method}</span>
                                  </div>
                                  <div>
                                    <span className="font-medium text-green-800">Confidence:</span>
                                    <span className="ml-1 text-green-700">{Math.round(alternative.integration.confidence * 100)}%</span>
                                  </div>
                                  <div>
                                    <span className="font-medium text-green-800">Files:</span>
                                    <span className="ml-1 text-green-700">{alternative.integration.file_locations?.length || 0} files</span>
                                  </div>
                                </div>
                              </div>

                              {/* MCP Server Capabilities */}
                              <div className="mb-4">
                                <h4 className="font-medium text-green-900 mb-2">Capabilities:</h4>
                                <div className="flex flex-wrap gap-2">
                                  {alternative.mcp_server.capabilities?.map((capability: string, idx: number) => (
                                    <Badge key={idx} variant="secondary" className="text-xs bg-green-100 text-green-800">
                                      {capability}
                                    </Badge>
                                  ))}
                                </div>
                              </div>

                              {/* Benefits */}
                              <div className="mb-4">
                                <h4 className="font-medium text-green-900 mb-2">Benefits:</h4>
                                <ul className="space-y-1">
                                  {alternative.benefits?.map((benefit: string, idx: number) => (
                                    <li key={idx} className="flex items-center text-sm text-green-800">
                                      <CheckCircleIcon className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                      {benefit}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              <div className="flex items-center justify-between">
                                <div className="text-sm text-green-700">
                                  <span className="font-medium">Implementation:</span> {alternative.migration_effort}
                                </div>
                                {alternative.mcp_server.marketplace_url && (
                                  <Button variant="outline" size="sm" asChild className="border-green-300 text-green-700 hover:bg-green-100">
                                    <a href={alternative.mcp_server.marketplace_url} target="_blank" rel="noopener noreferrer">
                                      View in Marketplace →
                                    </a>
                                  </Button>
                                )}
                              </div>
                            </div>
                            <Checkbox
                              checked={selectedTools.some(tool => tool.tool_name === alternative.mcp_server.server_name)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedTools([...selectedTools, {
                                    tool_name: alternative.mcp_server.server_name,
                                    description: alternative.mcp_server.description,
                                    category: 'INTEGRATION_REPLACEMENT'
                                  }]);
                                } else {
                                  setSelectedTools(selectedTools.filter(tool => tool.tool_name !== alternative.mcp_server.server_name));
                                }
                              }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Custom Development Needed */}
              {suggestionsData?.mcp_alternatives?.custom_development_needed &&
               suggestionsData.mcp_alternatives.custom_development_needed.length > 0 && (
                <Card className="border border-purple-200 shadow-sm">
                  <CardHeader className="bg-purple-50 border-b border-purple-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-purple-900">
                      <CpuChipIcon className="h-5 w-5 mr-3 text-purple-600" />
                      🛠️ Custom MCP Development Opportunities
                      <div className="ml-auto bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                        {suggestionsData.mcp_alternatives.custom_development_needed.length} needed
                      </div>
                    </CardTitle>
                    <CardDescription className="text-purple-700">
                      Integrations that require custom MCP server development for optimal AI integration
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      {suggestionsData.mcp_alternatives.custom_development_needed.map((custom: any, index: number) => (
                        <div key={index} className="border border-purple-100 rounded-lg p-6 bg-purple-50">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="text-lg font-semibold text-purple-900">
                                  {custom.custom_server_needed.server_name}
                                </h3>
                                <Badge variant="outline" className="text-xs">
                                  {custom.custom_server_needed.estimated_effort}
                                </Badge>
                              </div>
                              <p className="text-purple-800 text-sm mb-3">
                                {custom.custom_server_needed.description}
                              </p>

                              {/* Current Integration Details */}
                              <div className="bg-white border border-purple-200 rounded-lg p-4 mb-4">
                                <h4 className="font-medium text-purple-900 mb-2">Current Integration: {custom.integration.service_name}</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <span className="font-medium text-purple-800">Type:</span>
                                    <span className="ml-1 text-purple-700">{custom.integration.integration_type}</span>
                                  </div>
                                  <div>
                                    <span className="font-medium text-purple-800">Complexity:</span>
                                    <span className="ml-1 text-purple-700">{custom.integration.migration_complexity}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Technical Requirements */}
                              <div className="mb-4">
                                <h4 className="font-medium text-purple-900 mb-2">Technical Requirements:</h4>
                                <div className="space-y-1">
                                  {custom.custom_server_needed.technical_requirements?.map((req: string, idx: number) => (
                                    <div key={idx} className="flex items-center text-sm text-purple-800">
                                      <div className="w-2 h-2 bg-purple-600 rounded-full mr-2 flex-shrink-0"></div>
                                      {req}
                                    </div>
                                  ))}
                                </div>
                              </div>

                              <div className="text-sm text-purple-700">
                                <span className="font-medium">Development Effort:</span> {custom.custom_server_needed.estimated_effort}
                              </div>
                            </div>
                            <Checkbox
                              checked={selectedTools.some(tool => tool.tool_name === custom.custom_server_needed.server_name)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedTools([...selectedTools, {
                                    tool_name: custom.custom_server_needed.server_name,
                                    description: custom.custom_server_needed.description,
                                    category: 'CUSTOM_INTEGRATION'
                                  }]);
                                } else {
                                  setSelectedTools(selectedTools.filter(tool => tool.tool_name !== custom.custom_server_needed.server_name));
                                }
                              }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Legacy Integration Display (Fallback) */}
              {suggestionsData?.detected_integrations && suggestionsData.detected_integrations.length > 0 &&
               (!suggestionsData?.mcp_alternatives?.existing_mcp_servers || suggestionsData.mcp_alternatives.existing_mcp_servers.length === 0) &&
               (!suggestionsData?.mcp_alternatives?.custom_development_needed || suggestionsData.mcp_alternatives.custom_development_needed.length === 0) && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-gray-50 border-b border-gray-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                      <BoltIcon className="h-5 w-5 mr-3 text-primary" />
                      Detected Integrations
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      Third-party integrations found in your codebase
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      {suggestionsData.detected_integrations.map((integration: any, index: number) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 capitalize">
                                {integration.service_name} ({integration.integration_type})
                              </h3>
                              <p className="text-sm text-gray-600 mt-1">
                                {integration.description}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={integration.confidence > 0.8 ? "default" : "secondary"}>
                                {Math.round(integration.confidence * 100)}% confidence
                              </Badge>
                              <Badge variant={
                                integration.migration_complexity === "low" ? "default" :
                                integration.migration_complexity === "medium" ? "secondary" : "destructive"
                              }>
                                {integration.migration_complexity} complexity
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Detection Details</h4>
                              <ul className="text-sm text-gray-600 space-y-1">
                                <li>Method: {integration.detection_method}</li>
                                <li>Files: {integration.file_locations?.join(", ") || "N/A"}</li>
                                {integration.package_names?.length > 0 && (
                                  <li>Packages: {integration.package_names.join(", ")}</li>
                                )}
                              </ul>
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Current Usage</h4>
                              <div className="text-sm text-gray-600">
                                <p>This integration was detected in your codebase and could potentially be replaced with a lightweight MCP alternative.</p>
                              </div>
                            </div>
                          </div>

                          {/* MCP Alternatives */}
                          {suggestionsData.mcp_alternatives &&
                           suggestionsData.mcp_alternatives[`${integration.integration_type}_${integration.service_name}`] && (
                            <div className="border-t border-gray-200 pt-4">
                              <h4 className="font-medium text-gray-900 mb-3">🚀 Available MCP Alternatives</h4>
                              <div className="space-y-3">
                                {suggestionsData.mcp_alternatives[`${integration.integration_type}_${integration.service_name}`].tavily_results?.map((mcp: any, mcpIndex: number) => (
                                  <div key={mcpIndex} className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                                    <div className="flex items-start justify-between mb-2">
                                      <h5 className="font-medium text-gray-900">{mcp.name}</h5>
                                      <Badge variant="outline" className="text-primary border-primary">
                                        {Math.round(mcp.confidence_score * 100)}% match
                                      </Badge>
                                    </div>
                                    <p className="text-sm text-gray-600 mb-3">{mcp.description}</p>

                                    {mcp.benefits && mcp.benefits.length > 0 && (
                                      <div className="mb-3">
                                        <h6 className="text-sm font-medium text-gray-900 mb-1">Benefits:</h6>
                                        <ul className="text-sm text-gray-600 space-y-1">
                                          {mcp.benefits.map((benefit: string, benefitIndex: number) => (
                                            <li key={benefitIndex} className="flex items-center">
                                              <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                                              {benefit}
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    )}

                                    <div className="flex items-center justify-between">
                                      <span className="text-sm text-gray-500">
                                        Installation: {mcp.installation_complexity} complexity
                                      </span>
                                      {mcp.github_url && (
                                        <Button variant="outline" size="sm" asChild>
                                          <a href={mcp.github_url} target="_blank" rel="noopener noreferrer">
                                            View on GitHub
                                          </a>
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <BoltIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Integrations Detected</h3>
                      <p className="text-gray-600 max-w-md mx-auto">
                        We didn't find any third-party integrations that could be replaced with MCP alternatives.
                        This could mean your codebase is already optimized or uses custom implementations.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
              )}

              {/* No Integrations Found */}
              {(!suggestionsData?.detected_integrations || suggestionsData.detected_integrations.length === 0) &&
               (!suggestionsData?.mcp_alternatives?.existing_mcp_servers || suggestionsData.mcp_alternatives.existing_mcp_servers.length === 0) &&
               (!suggestionsData?.mcp_alternatives?.custom_development_needed || suggestionsData.mcp_alternatives.custom_development_needed.length === 0) && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardContent className="p-12">
                    <div className="text-center">
                      <BoltIcon className="h-16 w-16 text-gray-400 mx-auto mb-6" />
                      <h3 className="text-xl font-medium text-gray-900 mb-3">No Integration Opportunities Found</h3>
                      <p className="text-gray-600 max-w-lg mx-auto mb-6">
                        We didn't detect any third-party integrations that could be enhanced with MCP alternatives.
                        This could mean your codebase is already optimized, uses custom implementations, or the integrations
                        are too specialized for standard MCP replacements.
                      </p>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                        <h4 className="font-medium text-blue-900 mb-2">💡 Consider These Options:</h4>
                        <ul className="text-sm text-blue-800 space-y-1 text-left">
                          <li>• Check the Strategic Recommendations for custom MCP opportunities</li>
                          <li>• Review the Tool Selection tab for domain-specific MCP servers</li>
                          <li>• Consider creating MCP servers for your unique business logic</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="user-suggestions" className="p-6 space-y-6">
              <Card className="border border-blue-200 shadow-sm">
                <CardHeader className="bg-blue-50 border-b border-blue-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-blue-900">
                    <SparklesIcon className="h-5 w-5 mr-3 text-blue-600" />
                    Ask for Custom MCP Recommendations
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Describe what you want to achieve and get personalized MCP server recommendations based on your codebase
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="user-prompt" className="block text-sm font-medium text-gray-700 mb-2">
                        What would you like to achieve with MCP servers?
                      </label>
                      <textarea
                        id="user-prompt"
                        value={userPrompt}
                        onChange={(e) => setUserPrompt(e.target.value)}
                        placeholder="Example: I want AI to help me analyze my database queries and suggest optimizations..."
                        className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div className="flex items-center space-x-4">
                      <Button
                        onClick={generateUserSuggestions}
                        disabled={!userPrompt.trim() || userSuggestionsLoading}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {userSuggestionsLoading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <SparklesIcon className="h-4 w-4 mr-2" />
                            Get Recommendations
                          </>
                        )}
                      </Button>

                      {userPrompt.trim() && (
                        <Button
                          variant="outline"
                          onClick={() => {
                            setUserPrompt('');
                            setUserSuggestions(null);
                          }}
                        >
                          Clear
                        </Button>
                      )}
                    </div>

                    {/* Example prompts */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Example requests:</h4>
                      <div className="space-y-2">
                        {[
                          "Create an MCP server to expose my REST API endpoints for Claude to test and validate",
                          "I want Claude to query my database and generate insights from my business data",
                          "Build MCP tools so Claude can analyze and process files in my application's upload directory",
                          "Create MCP tools that let Claude execute my core business functions and workflows",
                          "I need MCP servers to expose my authentication and user management functions to Claude",
                          "Help me create MCP tools for Claude to interact with my payment processing system",
                          "Build an MCP server so Claude can manage my content management system programmatically"
                        ].map((example, index) => (
                          <button
                            key={index}
                            onClick={() => setUserPrompt(example)}
                            className="block text-left text-sm text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            "{example}"
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Suggestions Results */}
              {userSuggestions && (
                <Card className="border border-green-200 shadow-sm">
                  <CardHeader className="bg-green-50 border-b border-green-200">
                    <CardTitle className="flex items-center text-lg font-semibold text-green-900">
                      <CheckCircleIcon className="h-5 w-5 mr-3 text-green-600" />
                      Personalized Recommendations
                    </CardTitle>
                    <CardDescription className="text-green-700">
                      {userSuggestions.understanding}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      {userSuggestions.recommendations?.map((rec: any, index: number) => (
                        <div key={index} className="border border-green-100 rounded-lg p-4 bg-green-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h4 className="font-semibold text-green-900">{rec.server_name}</h4>
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                                  {rec.estimated_effort}
                                </Badge>
                                {rec.implementation_approach === 'existing_server' && (
                                  <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800">
                                    Ready to Use
                                  </Badge>
                                )}
                              </div>
                              <div className="space-y-2 text-sm">
                                <div>
                                  <span className="font-medium text-green-900">Addresses:</span>
                                  <span className="text-green-800 ml-1">{rec.addresses_request}</span>
                                </div>
                                <div>
                                  <span className="font-medium text-green-900">Business Value:</span>
                                  <span className="text-green-800 ml-1">{rec.business_value}</span>
                                </div>
                                {rec.existing_mcp_option && (
                                  <div>
                                    <span className="font-medium text-green-900">Existing Server:</span>
                                    <span className="text-green-800 ml-1">{rec.existing_mcp_option}</span>
                                    {rec.marketplace_url && (
                                      <a
                                        href={rec.marketplace_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-green-600 hover:text-green-800 text-xs underline ml-2"
                                      >
                                        View →
                                      </a>
                                    )}
                                  </div>
                                )}
                                {rec.exposed_functionality && rec.exposed_functionality.length > 0 && (
                                  <div>
                                    <span className="font-medium text-green-900">Exposed Functions:</span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {rec.exposed_functionality.slice(0, 5).map((func: string, idx: number) => (
                                        <Badge key={idx} variant="secondary" className="text-xs">
                                          {func}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            <Checkbox
                              checked={selectedTools.some(t => t.tool_name === rec.server_name)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedTools([...selectedTools, {
                                    tool_name: rec.server_name,
                                    description: rec.addresses_request,
                                    category: 'USER_REQUESTED'
                                  }]);
                                } else {
                                  setSelectedTools(selectedTools.filter(t => t.tool_name !== rec.server_name));
                                }
                              }}
                            />
                          </div>
                        </div>
                      ))}

                      {userSuggestions.next_steps && userSuggestions.next_steps.length > 0 && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <h4 className="font-medium text-blue-900 mb-2">Recommended Next Steps:</h4>
                          <ul className="list-disc list-inside space-y-1 text-sm text-blue-800">
                            {userSuggestions.next_steps.map((step: string, index: number) => (
                              <li key={index}>{step}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="generate" className="p-6 space-y-6">
              <Card className="border border-gray-200 shadow-sm">
                <CardHeader className="bg-gray-50 border-b border-gray-200">
                  <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                    <RocketLaunchIcon className="h-5 w-5 mr-3 text-primary" />
                    Generate MCP Server
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    Create a production-ready MCP server with your selected tools
                  </CardDescription>
                </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* Workflow Selection */}
                <Card className="border border-blue-200 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-lg font-semibold text-blue-900">
                      <SparklesIcon className="h-5 w-5 mr-3 text-blue-600" />
                      🎯 Complete Workflow Selection
                    </CardTitle>
                    <CardDescription className="text-blue-700">
                      Choose your implementation strategy and see the complete workflow from selection to deployment
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Workflow Strategy Selection */}
                    <div>
                      <label className="block text-sm font-medium text-blue-900 mb-3">
                        Select Your Implementation Strategy:
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div
                          className="border border-blue-200 rounded-lg p-4 bg-white hover:bg-blue-50 cursor-pointer transition-colors"
                          onClick={() => setWorkflowStrategy('quick_wins')}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <input
                              type="radio"
                              name="workflow"
                              value="quick_wins"
                              className="text-blue-600"
                              checked={workflowStrategy === 'quick_wins'}
                              onChange={(e) => setWorkflowStrategy(e.target.value)}
                            />
                            <h4 className="font-medium text-blue-900">🚀 Quick Wins</h4>
                          </div>
                          <p className="text-sm text-blue-700">
                            Deploy existing MCP servers from marketplace (1 week)
                          </p>
                        </div>
                        <div
                          className="border border-blue-200 rounded-lg p-4 bg-white hover:bg-blue-50 cursor-pointer transition-colors"
                          onClick={() => setWorkflowStrategy('strategic')}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <input
                              type="radio"
                              name="workflow"
                              value="strategic"
                              className="text-blue-600"
                              checked={workflowStrategy === 'strategic'}
                              onChange={(e) => setWorkflowStrategy(e.target.value)}
                            />
                            <h4 className="font-medium text-blue-900">⚡ Strategic Mix</h4>
                          </div>
                          <p className="text-sm text-blue-700">
                            Combine marketplace servers with custom development (2-3 weeks)
                          </p>
                        </div>
                        <div
                          className="border border-blue-200 rounded-lg p-4 bg-white hover:bg-blue-50 cursor-pointer transition-colors"
                          onClick={() => setWorkflowStrategy('custom')}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <input
                              type="radio"
                              name="workflow"
                              value="custom"
                              className="text-blue-600"
                              checked={workflowStrategy === 'custom'}
                              onChange={(e) => setWorkflowStrategy(e.target.value)}
                            />
                            <h4 className="font-medium text-blue-900">🛠️ Full Custom</h4>
                          </div>
                          <p className="text-sm text-blue-700">
                            Build domain-specific MCP servers from scratch (4-6 weeks)
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Complete Workflow Display */}
                    <div className="bg-white border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-3">📋 Complete Implementation Workflow:</h4>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">1</div>
                          <div>
                            <h5 className="font-medium text-gray-900">Tool Selection & Validation</h5>
                            <p className="text-sm text-gray-600">Review and select MCP tools from strategic recommendations</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">2</div>
                          <div>
                            <h5 className="font-medium text-gray-900">Architecture Configuration</h5>
                            <p className="text-sm text-gray-600">Choose target language and hosting architecture</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">3</div>
                          <div>
                            <h5 className="font-medium text-gray-900">Code Generation</h5>
                            <p className="text-sm text-gray-600">Generate production-ready MCP server with business logic integration</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">4</div>
                          <div>
                            <h5 className="font-medium text-gray-900">Download & Deploy</h5>
                            <p className="text-sm text-gray-600">Download complete project with setup instructions and deployment guides</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-xs font-medium">5</div>
                          <div>
                            <h5 className="font-medium text-gray-900">Integration & Testing</h5>
                            <p className="text-sm text-gray-600">Integrate with Claude/AI assistants and validate functionality</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Selection Summary */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <CheckCircleIcon className="h-4 w-4 text-primary" />
                    </div>
                    <h4 className="font-medium text-gray-900">Selection Summary</h4>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-primary mb-1">{selectedTools.length}</div>
                      <div className="text-sm text-gray-600">Selected Tools</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-green-600 mb-1">
                        {selectedTools.filter(tool => tool.category === 'QUICK_WINS' || tool.category === 'INTEGRATION_REPLACEMENT').length}
                      </div>
                      <div className="text-sm text-gray-600">Quick Wins</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-purple-600 mb-1">
                        {selectedTools.filter(tool => tool.category === 'CUSTOM_DEVELOPMENT' || tool.category === 'CUSTOM_INTEGRATION').length}
                      </div>
                      <div className="text-sm text-gray-600">Custom Development</div>
                    </div>
                  </div>

                  {/* Selected Tools List */}
                  {selectedTools.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h5 className="font-medium text-gray-900 mb-2">Selected Tools:</h5>
                      <div className="space-y-2">
                        {selectedTools.map((tool, index) => (
                          <div key={index} className="flex items-center justify-between bg-white rounded-lg p-2 border">
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                {tool.category?.replace('_', ' ') || 'Tool'}
                              </Badge>
                              <span className="text-sm font-medium text-gray-900">{tool.tool_name}</span>
                            </div>
                            <button
                              onClick={() => setSelectedTools(selectedTools.filter((_, i) => i !== index))}
                              className="text-gray-400 hover:text-red-500 transition-colors"
                            >
                              <XCircleIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Use Case Selection */}
                {selectedTools.length > 0 && (
                  <Card className="border border-purple-200 shadow-sm">
                    <CardHeader className="bg-purple-50 border-b border-purple-200">
                      <CardTitle className="flex items-center text-lg font-semibold text-purple-900">
                        <CpuChipIcon className="h-5 w-5 mr-3 text-purple-600" />
                        🎯 Use Case Configuration
                      </CardTitle>
                      <CardDescription className="text-purple-700">
                        Configure specific use cases for your selected MCP tools
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="space-y-6">
                        {selectedTools.map((tool, index) => (
                          <div key={index} className="border border-purple-100 rounded-lg p-4 bg-purple-50">
                            <div className="flex items-start justify-between mb-3">
                              <div>
                                <h4 className="font-semibold text-purple-900">{tool.tool_name}</h4>
                                <p className="text-sm text-purple-700 mt-1">{tool.description}</p>
                              </div>
                              <Badge variant="outline" className="bg-purple-100 text-purple-800">
                                {tool.category?.replace('_', ' ') || 'Tool'}
                              </Badge>
                            </div>

                            {/* Tool Implementation Details */}
                            <div className="bg-white border border-purple-100 rounded-lg p-3 mt-3">
                              <div className="text-sm text-purple-700">
                                <span className="font-medium">Implementation:</span> This tool will be generated with comprehensive functionality including business logic integration, error handling, authentication, and production-ready features.
                              </div>
                              {tool.category && (
                                <div className="text-xs text-purple-600 mt-1">
                                  <span className="font-medium">Category:</span> {tool.category.replace('_', ' ').toLowerCase()}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Language Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Language
                      {languageRecommendation && (
                        <span className="ml-2 text-xs text-green-600 font-medium">
                          ⭐ {languageRecommendation.recommended_language} recommended for {languageRecommendation.primary_language || 'this'} codebase
                        </span>
                      )}
                    </label>
                    {languageRecommendation && languageRecommendation.reasoning && (
                      <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="text-sm text-blue-800">
                          <span className="font-medium">💡 Recommendation:</span> {languageRecommendation.reasoning.explanation}
                        </div>
                        {languageRecommendation.language_distribution && Object.keys(languageRecommendation.language_distribution).length > 0 && (
                          <div className="mt-2 text-xs text-blue-600">
                            <span className="font-medium">Repository languages:</span> {
                              Object.entries(languageRecommendation.language_distribution)
                                .sort(([,a], [,b]) => (b as number) - (a as number))
                                .slice(0, 3)
                                .map(([lang, percent]) => `${lang} (${percent}%)`)
                                .join(', ')
                            }
                          </div>
                        )}
                      </div>
                    )}
                    <Select value={targetLanguage} onValueChange={setTargetLanguage}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="python">
                          <div className="flex flex-col">
                            <span className="font-medium">Python</span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'python'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'python'
                                ? 'text-blue-600'
                                : 'text-green-600'
                            }`}>
                              {languageRecommendation?.recommended_language === 'python' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'python' && '🎯 Matches your repository - '}
                              Most popular, fastest development
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="typescript">
                          <div className="flex flex-col">
                            <span className="font-medium">TypeScript</span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'typescript'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'typescript'
                                ? 'text-blue-600'
                                : 'text-blue-600'
                            }`}>
                              {languageRecommendation?.recommended_language === 'typescript' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'typescript' && '🎯 Matches your repository - '}
                              Best for web developers
                            </span>
                          </div>
                        </SelectItem>

                        {/* Separator */}
                        <div className="px-2 py-1 text-xs text-gray-500 border-t border-gray-100 mt-2">
                          Advanced Options (Less Common)
                        </div>
                        <SelectItem value="javascript">
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-600">JavaScript</span>
                            <span className="text-xs text-gray-500">Use TypeScript instead for better experience</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="go">
                          <div className="flex flex-col">
                            <span className={`font-medium ${
                              languageRecommendation?.primary_language === 'go' ? 'text-blue-600' : 'text-gray-600'
                            }`}>
                              Go {languageRecommendation?.primary_language === 'go' && '🎯'}
                            </span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'go'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'go'
                                ? 'text-blue-600'
                                : 'text-gray-500'
                            }`}>
                              {languageRecommendation?.recommended_language === 'go' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'go' && 'Matches your repository - '}
                              High performance (enterprise only)
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="rust">
                          <div className="flex flex-col">
                            <span className={`font-medium ${
                              languageRecommendation?.primary_language === 'rust' ? 'text-blue-600' : 'text-gray-600'
                            }`}>
                              Rust {languageRecommendation?.primary_language === 'rust' && '🎯'}
                            </span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'rust'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'rust'
                                ? 'text-blue-600'
                                : 'text-gray-500'
                            }`}>
                              {languageRecommendation?.recommended_language === 'rust' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'rust' && 'Matches your repository - '}
                              Maximum performance (expert level)
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="java">
                          <div className="flex flex-col">
                            <span className={`font-medium ${
                              languageRecommendation?.primary_language === 'java' ? 'text-blue-600' : 'text-gray-600'
                            }`}>
                              Java {languageRecommendation?.primary_language === 'java' && '🎯'}
                            </span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'java'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'java'
                                ? 'text-blue-600'
                                : 'text-gray-500'
                            }`}>
                              {languageRecommendation?.recommended_language === 'java' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'java' && 'Matches your repository - '}
                              Enterprise applications, Spring ecosystem
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="csharp">
                          <div className="flex flex-col">
                            <span className={`font-medium ${
                              languageRecommendation?.primary_language === 'c#' ? 'text-blue-600' : 'text-gray-600'
                            }`}>
                              C# {languageRecommendation?.primary_language === 'c#' && '🎯'}
                            </span>
                            <span className={`text-xs ${
                              languageRecommendation?.recommended_language === 'csharp'
                                ? 'text-green-600 font-medium'
                                : languageRecommendation?.primary_language === 'c#'
                                ? 'text-blue-600'
                                : 'text-gray-500'
                            }`}>
                              {languageRecommendation?.recommended_language === 'csharp' && '⭐ Recommended for your codebase - '}
                              {languageRecommendation?.primary_language === 'c#' && 'Matches your repository - '}
                              .NET ecosystem, Windows applications
                            </span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hosting Architecture
                    </label>
                    <Select value={hostingArchitecture} onValueChange={setHostingArchitecture}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="http-sse">
                          <div className="flex flex-col">
                            <span className="font-medium">HTTP/SSE Server</span>
                            <span className="text-xs text-gray-500">Recommended - Remote hosting</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="websocket">
                          <div className="flex flex-col">
                            <span className="font-medium">WebSocket Server</span>
                            <span className="text-xs text-gray-500">Real-time bidirectional</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="stdio">
                          <div className="flex flex-col">
                            <span className="font-medium">Local STDIO</span>
                            <span className="text-xs text-gray-500">Direct process communication</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="docker">
                          <div className="flex flex-col">
                            <span className="font-medium">Docker Container</span>
                            <span className="text-xs text-gray-500">Isolated deployment</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Architecture Description */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">
                    {hostingArchitecture === 'http-sse' && 'HTTP/SSE Server Architecture'}
                    {hostingArchitecture === 'websocket' && 'WebSocket Server Architecture'}
                    {hostingArchitecture === 'stdio' && 'Local STDIO Architecture'}
                    {hostingArchitecture === 'docker' && 'Docker Container Architecture'}
                  </h4>
                  <p className="text-sm text-blue-700">
                    {hostingArchitecture === 'http-sse' && 'Remote server that communicates via HTTP requests and Server-Sent Events. Best for cloud deployment and scaling. Supports multiple concurrent clients.'}
                    {hostingArchitecture === 'websocket' && 'Real-time bidirectional communication over WebSocket protocol. Ideal for interactive applications requiring low latency.'}
                    {hostingArchitecture === 'stdio' && 'Direct process communication through standard input/output. Fastest option for local usage and development.'}
                    {hostingArchitecture === 'docker' && 'Containerized deployment with isolated environment. Perfect for microservices and cloud-native architectures.'}
                  </p>
                </div>

                {/* Generated Files Structure */}
                {generatedProjectStructure && generatedProjectStructure.length > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
                      <h4 className="font-medium text-green-900">Generation Complete!</h4>
                    </div>
                    <p className="text-sm text-green-700 mb-4">
                      Your MCP server has been generated successfully. Review the file structure below:
                    </p>
                    
                    <div className="bg-white rounded-lg border p-3 max-h-60 overflow-y-auto">
                      <div className="space-y-1">
                        {generatedProjectStructure.map((file, index) => {
                          const isFolder = file.endsWith('/') || (!file.includes('.') && file.includes('/'));
                          const fileName = file.split('/').pop() || file;
                          const depth = (file.match(/\//g) || []).length;
                          
                          return (
                            <div 
                              key={index} 
                              className="flex items-center space-x-2 text-sm py-0.5"
                              style={{ paddingLeft: `${depth * 16}px` }}
                            >
                              {isFolder ? (
                                <FolderIcon className="h-3 w-3 text-blue-600 flex-shrink-0" />
                              ) : (
                                <DocumentIcon className="h-3 w-3 text-gray-600 flex-shrink-0" />
                              )}
                              <span className={isFolder ? "font-medium text-gray-900" : "text-gray-700"}>
                                {fileName}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    
                    {setupInstructions && (
                      <div className="mt-3 bg-white rounded-lg border p-3">
                        <h5 className="font-medium text-gray-900 mb-2">Setup Instructions</h5>
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap max-h-24 overflow-y-auto">
                          {setupInstructions}
                        </pre>
                      </div>
                    )}
                  </div>
                )}

                {/* Generate and Download Buttons */}
                <div className="pt-4 border-t space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      {selectedTools.length === 0 && "Select at least one tool to generate server"}
                      {generatedZipPath && "Ready to download your MCP server"}
                      {generationInProgress && "Generation in progress... This may take 1-3 minutes for complex repositories."}
                    </div>
                    <Button
                      onClick={generateMCPServer}
                      disabled={selectedTools.length === 0 || generationInProgress}
                      className="bg-primary hover:bg-primary/90"
                    >
                      {generationInProgress ? (
                        <>
                          <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <RocketLaunchIcon className="h-4 w-4 mr-2" />
                          Generate Server
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {generationInProgress && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-blue-600 mr-2 animate-spin" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-900">
                            Generating your MCP server...
                          </h4>
                          <p className="text-xs text-blue-700 mt-1">
                            This process typically takes 1-3 minutes. We&apos;re analyzing your repository, 
                            generating tools, and creating a complete MCP server package.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {generatedZipPath && (
                    <div className="flex justify-end">
                      <Button
                        onClick={downloadMCPServer}
                        disabled={downloadInProgress}
                        variant="outline"
                        className="border-green-600 text-green-600 hover:bg-green-50"
                      >
                        {downloadInProgress ? (
                          <>
                            <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                            Downloading...
                          </>
                        ) : (
                          <>
                            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                            Download Server
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            </TabsContent>
          </Tabs>
        </div>



        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {error.includes('quota') ? 'AI Service Quota Exceeded' :
                     error.includes('rate limit') ? 'AI Service Rate Limited' :
                     error.includes('authentication') ? 'AI Service Authentication Error' :
                     error.includes('model') ? 'AI Model Unavailable' :
                     'MCP Suggestions Error'}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                    {error.includes('quota') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-xs text-red-600">
                          <strong>Next Steps:</strong>
                          <br />• Check your AI service billing dashboard
                          <br />• Upgrade your plan or add credits
                          <br />• Try again once quota is restored
                        </p>
                      </div>
                    )}
                    {error.includes('rate limit') && (
                      <div className="mt-3 p-3 bg-red-100 rounded-md">
                        <p className="text-xs text-red-600">
                          <strong>Please wait a few minutes and try again.</strong>
                          <br />The AI service has temporary rate limits to ensure fair usage.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}