"""
MCP Generation API endpoints
Handles MCP tool suggestions and server code generation
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import os

from ..database import get_db
from ..models import User, RepoAnalysis
from ..utils.auth import get_current_user
from ..services.mcp_code_generator import MCPCodeGeneratorService
from ..tasks.enhanced_analysis import generate_mcp_suggestions_only

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize MCP code generator
mcp_generator = MCPCodeGeneratorService()


def transform_suggestions_for_frontend(ai_suggestions: Dict[str, Any]) -> Dict[str, Any]:
    """Transform enhanced AI suggestions to match frontend interface expectations"""

    try:
        # Initialize categories variable
        categories = {}

        # Handle enhanced analysis structure - check if it's already in the new format
        if "categories" in ai_suggestions and "repository_analysis" in ai_suggestions:
            # New enhanced format - return as-is with minimal transformation
            categories = ai_suggestions.get("categories", {})

            # Ensure all tools have required fields
            for category_name, tools in categories.items():
                if not isinstance(tools, list):
                    continue
                for tool in tools:
                    if not isinstance(tool, dict):
                        continue
                    # Ensure complexity_level is properly set
                    if "complexity_level" not in tool and "estimated_effort_hours" in tool:
                        hours = tool["estimated_effort_hours"]
                        if hours <= 8:
                            tool["complexity_level"] = "low"
                        elif hours <= 16:
                            tool["complexity_level"] = "medium"
                        else:
                            tool["complexity_level"] = "high"
                    elif "complexity_level" not in tool:
                        tool["complexity_level"] = "medium"

                    # Ensure required fields exist
                    tool.setdefault("tool_name", tool.get("name", "Unknown Tool"))
                    tool.setdefault("description", "No description available")
                    tool.setdefault("business_value", "No description available")
                    tool.setdefault("input_schema", {})
                    tool.setdefault("output_schema", {})
                    tool.setdefault("implementation_hints", "")
                    tool.setdefault("use_cases", [])
                    tool.setdefault("dependencies", [])
                    tool.setdefault("error_scenarios", [])

            # Include new strategic structure if available
            result = {
                "categories": categories,
                "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
                "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
                "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0),
                "repository_analysis": ai_suggestions.get("repository_analysis", {}),
                "tool_generation_strategy": ai_suggestions.get("tool_generation_strategy", {})
            }

            # Add new strategic structure if available
            if "codebase_opportunities" in ai_suggestions:
                result["codebase_opportunities"] = ai_suggestions["codebase_opportunities"]
            if "marketplace_recommendations" in ai_suggestions:
                result["marketplace_recommendations"] = ai_suggestions["marketplace_recommendations"]
            if "strategic_recommendations" in ai_suggestions:
                result["strategic_recommendations"] = ai_suggestions["strategic_recommendations"]
            if "integration_architecture" in ai_suggestions:
                result["integration_architecture"] = ai_suggestions["integration_architecture"]
            if "success_metrics" in ai_suggestions:
                result["success_metrics"] = ai_suggestions["success_metrics"]

            return result

        # Legacy format transformation (for older analyses)
        def get_complexity_level(implementation_effort: str) -> str:
            """Convert implementation effort to complexity level"""
            effort_lower = implementation_effort.lower() if implementation_effort else "medium"
            if "low" in effort_lower or "easy" in effort_lower or "simple" in effort_lower:
                return "low"
            elif "high" in effort_lower or "hard" in effort_lower or "complex" in effort_lower:
                return "high"
            else:
                return "medium"

        # Transform categories with proper field mapping for legacy format
        transformed_categories = {}
        categories = ai_suggestions.get("categories", {})

        for category_name, tools in categories.items():
            transformed_tools = []

            for tool in tools:
                # Transform each tool to match frontend interface
                transformed_tool = {
                    "tool_name": tool.get("tool_name", tool.get("name", "Unknown Tool")),
                    "description": tool.get("description", tool.get("business_value", "No description available")),
                    "business_value": tool.get("business_value", "No description available"),
                    "complexity_level": tool.get("complexity_level", get_complexity_level(tool.get("implementation_effort", "medium"))),
                    "input_schema": tool.get("input_schema", {}),
                    "output_schema": tool.get("output_schema", {}),
                    "implementation_hints": tool.get("implementation_hints", ""),
                    "use_cases": tool.get("use_cases", []),
                    "dependencies": tool.get("dependencies", []),
                    "error_scenarios": tool.get("error_scenarios", [])
                }
                transformed_tools.append(transformed_tool)

            transformed_categories[category_name] = transformed_tools

        # Return transformed suggestions
        return {
            "categories": transformed_categories,
            "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
            "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
            "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0)
        }

    except Exception as e:
        logger.error(f"Error transforming suggestions for frontend: {str(e)}")
        # Return empty structure on error
        return {
            "categories": {},
            "prioritized_recommendations": [],
            "implementation_roadmap": {},
            "total_tools_suggested": 0
        }


# Request/Response Models
class SelectedTool(BaseModel):
    tool_name: str
    description: str
    category: str

class GenerateMCPServerRequest(BaseModel):
    analysis_id: int
    selected_tools: List[SelectedTool]  # List of tool objects
    target_language: str = "typescript"
    hosting_architecture: str = "http-sse"
    customization_options: Optional[Dict[str, Any]] = None

class UserMCPSuggestionRequest(BaseModel):
    analysis_id: int
    user_prompt: str
    focus_areas: Optional[List[str]] = None  # e.g., ["api_endpoints", "database", "file_processing"]


# Endpoints
@router.get("/{analysis_id}/suggestions")
async def get_mcp_suggestions(
    analysis_id: int,
    regenerate: bool = Query(False, description="Force regenerate suggestions"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get MCP tool suggestions for an analysis"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    logger.info(f"Analysis status: {analysis.status}")
    logger.info(f"Analysis error_message: {analysis.error_message}")

    # Allow suggestions generation for failed analyses if they have basic analysis results
    if analysis.status not in ["completed", "failed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Analysis must be completed or failed with results before generating suggestions. Current status: {analysis.status}. Error: {analysis.error_message}"
        )

    # For failed analyses, check if we have enough data to generate suggestions
    if analysis.status == "failed":
        analysis_results = analysis.analysis_results or {}
        if not analysis_results.get("repository_info") and not analysis_results.get("dependencies"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed analysis does not have sufficient data for suggestion generation. Error: {analysis.error_message}"
            )
    
    # Check if suggestions already exist and user doesn't want to regenerate
    analysis_results = analysis.analysis_results or {}
    logger.info(f"Analysis results keys: {list(analysis_results.keys())}")
    
    if not regenerate and "ai_suggestions" in analysis_results:
        # Transform suggestions to frontend format
        ai_suggestions = analysis_results.get("ai_suggestions", {})
        logger.info(f"AI suggestions structure: {ai_suggestions.keys() if ai_suggestions else 'Empty'}")
        logger.info(f"AI suggestions sample: {str(ai_suggestions)[:500]}...")
        
        try:
            transformed_suggestions = transform_suggestions_for_frontend(ai_suggestions)
            logger.info(f"Transformed suggestions successfully")
            
            # Return existing suggestions with enhanced structure including detected integrations and MCP alternatives
            return {
                "analysis_id": analysis_id,
                "suggestions_available": True,
                "mcp_suggestions": transformed_suggestions,
                "detected_integrations": analysis_results.get("ai_analysis", {}).get("detected_integrations", []),
                "mcp_alternatives": analysis_results.get("ai_analysis", {}).get("mcp_alternatives", {}),
                "business_analysis": analysis_results.get("business_analysis", analysis_results.get("comprehensive_analysis", {})),
                "confidence_score": analysis_results.get("confidence_score", 0.0),
                "implementation_complexity": analysis_results.get("implementation_complexity", {})
            }
        except Exception as e:
            logger.error(f"Error transforming suggestions: {str(e)}")
            logger.exception("Full error details:")
            # Fall through to regeneration logic
    
    # Generate new suggestions
    try:
        # Check if user has GitHub token
        github_token = getattr(current_user, 'github_token', None)
        if not github_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="GitHub token required for suggestion generation"
            )
            
        task = generate_mcp_suggestions_only.delay(analysis_id, github_token)
        
        # Include detected integrations even when generating new suggestions
        analysis_results = analysis.analysis_results or {}
        detected_integrations = analysis_results.get("ai_analysis", {}).get("detected_integrations", [])

        return {
            "analysis_id": analysis_id,
            "suggestions_available": False,
            "detected_integrations": detected_integrations,
            "task_id": task.id,
            "status": "generating",
            "message": "MCP suggestions are being generated. Please check back in a moment."
        }
        
    except Exception as e:
        logger.error(f"Failed to start MCP suggestions generation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP suggestions: {str(e)}"
        )


@router.post("/{analysis_id}/generate-server")
async def generate_mcp_server(
    analysis_id: int,
    request: GenerateMCPServerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP server code based on selected tools"""
    
    logger.info(f"MCP Generation Request - Language: {request.target_language}, Architecture: {request.hosting_architecture}, Tools: {len(request.selected_tools)}")
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating MCP server"
        )
    
    analysis_results = analysis.analysis_results or {}
    
    # Check if MCP suggestions are available
    if "ai_suggestions" not in analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="MCP suggestions not available. Please generate suggestions first."
        )
    
    # Get MCP suggestions and find selected tools
    ai_suggestions = analysis_results.get("ai_suggestions", {})
    
    # Transform suggestions to frontend format to ensure consistent tool_name field
    mcp_suggestions = transform_suggestions_for_frontend(ai_suggestions)
    all_tools = {}
    
    # Collect all tools from all categories
    for tools in mcp_suggestions.get("categories", {}).values():
        for tool in tools:
            all_tools[tool["tool_name"]] = tool
    
    # Filter selected tools
    selected_tools = []
    for selected_tool in request.selected_tools:
        tool_name = selected_tool.tool_name
        if tool_name in all_tools:
            # Use the tool from suggestions but add category info from selection
            tool_data = all_tools[tool_name].copy()
            tool_data["selection_category"] = selected_tool.category
            selected_tools.append(tool_data)
        else:
            # Handle tools that might be from new strategic recommendations
            logger.info(f"Tool '{tool_name}' not found in legacy categories, treating as strategic recommendation")
            selected_tools.append({
                "tool_name": tool_name,
                "description": selected_tool.description,
                "category": selected_tool.category,
                "selection_category": selected_tool.category,
                "business_value": selected_tool.description,
                "complexity_level": "medium",
                "estimated_effort_hours": 24
            })
    
    if not selected_tools:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid tools selected for generation"
        )
    
    # Prepare repository context
    repo_context = {
        "name": analysis.repo_name,
        "owner": analysis.repo_owner,
        "url": analysis.repo_url,
        "language": analysis_results.get("repository_info", {}).get("language"),
        "description": analysis_results.get("repository_info", {}).get("description")
    }
    
    # Generate MCP server code
    try:
        generator = MCPCodeGeneratorService()
        
        generation_result = await generator.generate_mcp_server(
            selected_tools=selected_tools,
            target_language=request.target_language,
            hosting_architecture=request.hosting_architecture,
            repo_context=repo_context,
            analysis_data=analysis_results,
            customization_options=request.customization_options
        )
        
        return {
            "success": True,
            "analysis_id": analysis_id,
            "generation_result": generation_result,
            "download_ready": True
        }
        
    except Exception as e:
        logger.error(f"MCP server generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP server: {str(e)}"
        )


@router.post("/{analysis_id}/user-suggestions")
async def get_user_driven_mcp_suggestions(
    analysis_id: int,
    request: UserMCPSuggestionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP suggestions based on user prompt and indexed code"""

    logger.info(f"User-driven MCP suggestions request for analysis {analysis_id}: {request.user_prompt}")

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating suggestions"
        )

    try:
        # Get analysis results
        analysis_results = analysis.analysis_results or {}

        # Generate user-driven suggestions
        from ..services.user_mcp_suggestion_service import UserMCPSuggestionService
        suggestion_service = UserMCPSuggestionService()

        suggestions = await suggestion_service.generate_user_driven_suggestions(
            user_prompt=request.user_prompt,
            analysis_data=analysis_results,
            focus_areas=request.focus_areas or []
        )

        return {
            "success": True,
            "analysis_id": analysis_id,
            "user_prompt": request.user_prompt,
            "suggestions": suggestions,
            "total_suggestions": len(suggestions.get("recommendations", []))
        }

    except Exception as e:
        logger.error(f"User-driven suggestions failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate user-driven suggestions: {str(e)}"
        )


@router.get("/{analysis_id}/download-server")
async def download_mcp_server(
    analysis_id: int,
    zip_file_path: str = Query(..., description="Path to generated ZIP file"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download generated MCP server ZIP file"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Verify ZIP file exists
    if not os.path.exists(zip_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generated MCP server file not found"
        )
    
    # Return file for download
    filename = f"mcp-server-{analysis.repo_name}-{analysis_id}.zip"
    
    return FileResponse(
        zip_file_path,
        media_type="application/zip",
        filename=filename,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/languages/supported")
async def get_supported_languages():
    """Get list of supported programming languages for MCP server generation"""
    
    return {
        "supported_languages": [
            {
                "code": "python",
                "name": "Python",
                "description": "Python MCP server using @modelcontextprotocol/server-python",
                "features": ["Type hints", "Pydantic validation", "Async support", "Rich ecosystem"],
                "recommended": True
            },
            {
                "code": "typescript", 
                "name": "TypeScript",
                "description": "TypeScript MCP server using @modelcontextprotocol/sdk",
                "features": ["Strong typing", "Modern JS features", "Node.js ecosystem", "IDE support"],
                "recommended": True
            },
            {
                "code": "javascript",
                "name": "JavaScript",
                "description": "JavaScript MCP server using @modelcontextprotocol/sdk", 
                "features": ["Quick development", "Node.js ecosystem", "Wide compatibility"],
                "recommended": False
            }
        ],
        "default_language": "python"
    }


@router.get("/analysis/{analysis_id}/language-recommendation")
async def get_language_recommendation(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recommended MCP server language based on repository analysis"""

    logger.info(f"Language recommendation request for analysis_id: {analysis_id} (type: {type(analysis_id)})")

    # Get analysis record
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    logger.info(f"Found analysis: {analysis is not None}")

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if not analysis.analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis not completed yet"
        )

    try:
        # Extract repository context and analysis data
        analysis_results = analysis.analysis_results
        repo_context = analysis_results.get('repository_info', {})

        logger.info(f"Getting language recommendation for analysis_id: {analysis_id}")
        logger.info(f"repo_context type: {type(repo_context)}, content: {repo_context}")

        # Get language recommendation
        recommended_language = mcp_generator.get_recommended_language(repo_context, analysis_results)
        logger.info(f"recommended_language: {recommended_language} (type: {type(recommended_language)})")

        # Ensure recommended_language is a string
        if not isinstance(recommended_language, str):
            logger.error(f"get_recommended_language returned non-string: {type(recommended_language)} = {recommended_language}")
            recommended_language = 'python'  # Safe fallback

        # Get primary language from repository
        primary_language = repo_context.get('language', '')
        logger.info(f"primary_language before processing: {primary_language} (type: {type(primary_language)})")

        if primary_language and isinstance(primary_language, str):
            primary_language = primary_language.lower()
        else:
            primary_language = ''

        logger.info(f"primary_language after processing: {primary_language}")

        # Get language distribution
        code_structure = analysis_results.get('code_structure', {})
        languages = code_structure.get('languages', {})

        # Calculate language percentages (handle mixed types in values)
        total_files = 0
        if languages:
            # Convert all values to integers, filtering out non-numeric values
            numeric_values = []
            for value in languages.values():
                try:
                    if isinstance(value, (int, float)):
                        numeric_values.append(int(value))
                    elif isinstance(value, str) and value.isdigit():
                        numeric_values.append(int(value))
                except (ValueError, TypeError):
                    continue
            total_files = sum(numeric_values) if numeric_values else 0

        language_percentages = {}
        if total_files > 0:
            language_percentages = {}
            for lang, count in languages.items():
                try:
                    if isinstance(count, (int, float)):
                        numeric_count = int(count)
                    elif isinstance(count, str) and count.isdigit():
                        numeric_count = int(count)
                    else:
                        continue
                    language_percentages[lang] = round((numeric_count / total_files) * 100, 1)
                except (ValueError, TypeError, ZeroDivisionError):
                    continue

        logger.info("Building response object...")

        # Build reasoning object safely
        primary_match = primary_language == recommended_language.replace('csharp', 'c#')
        logger.info(f"primary_match: {primary_match}")

        explanation = f"Recommended {recommended_language} because your repository is primarily {primary_language or 'unknown'}" if primary_language else f"Recommended {recommended_language} as the default choice"
        logger.info(f"explanation: {explanation}")

        benefits = _get_language_benefits(recommended_language, primary_language)
        logger.info(f"benefits: {benefits}")

        alternatives = _get_alternative_languages(recommended_language, primary_language)
        logger.info(f"alternatives: {alternatives}")

        response = {
            "recommended_language": recommended_language,
            "primary_language": primary_language,
            "language_distribution": language_percentages,
            "reasoning": {
                "primary_match": primary_match,
                "explanation": explanation,
                "benefits": benefits
            },
            "alternatives": alternatives
        }

        logger.info("Response built successfully")
        return response

    except Exception as e:
        import traceback
        logger.error(f"Error getting language recommendation: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get language recommendation: {str(e)}"
        )


def _get_language_benefits(recommended_lang: str, primary_lang: str) -> List[str]:
    """Get benefits of using the recommended language"""
    benefits = {
        'python': [
            "Fastest development cycle",
            "Best MCP ecosystem support",
            "Easy integration with AI/ML libraries",
            "Extensive documentation and examples"
        ],
        'typescript': [
            "Strong typing prevents runtime errors",
            "Excellent IDE support and tooling",
            "Large Node.js ecosystem",
            "Good for web developers"
        ],
        'go': [
            "High performance and low memory usage",
            "Excellent for concurrent operations",
            "Single binary deployment",
            "Strong standard library"
        ],
        'rust': [
            "Maximum performance and memory safety",
            "Zero-cost abstractions",
            "Excellent for system-level operations",
            "Growing ecosystem"
        ],
        'java': [
            "Enterprise-grade reliability",
            "Mature ecosystem and tooling",
            "Strong typing and performance",
            "Wide platform support"
        ],
        'csharp': [
            "Excellent .NET integration",
            "Strong typing and performance",
            "Rich development tools",
            "Good for Windows environments"
        ]
    }

    base_benefits = benefits.get(recommended_lang, ["Good general-purpose choice"])

    # Add native language benefit if matching
    if primary_lang and recommended_lang == primary_lang.replace('c#', 'csharp'):
        base_benefits.insert(0, f"Matches your repository's primary language ({primary_lang})")

    return base_benefits


def _get_alternative_languages(recommended_lang: str, primary_lang: str) -> List[Dict[str, Any]]:
    """Get alternative language options with explanations"""
    all_languages = {
        'python': {
            'name': 'Python',
            'popularity': 'Very High',
            'difficulty': 'Easy',
            'use_case': 'General purpose, AI/ML, rapid development'
        },
        'typescript': {
            'name': 'TypeScript',
            'popularity': 'High',
            'difficulty': 'Medium',
            'use_case': 'Web development, type-safe JavaScript'
        },
        'go': {
            'name': 'Go',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'High performance, microservices, CLI tools'
        },
        'rust': {
            'name': 'Rust',
            'popularity': 'Low',
            'difficulty': 'Hard',
            'use_case': 'System programming, maximum performance'
        },
        'java': {
            'name': 'Java',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'Enterprise applications, Android development'
        },
        'csharp': {
            'name': 'C#',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'Windows applications, .NET ecosystem'
        }
    }

    # Return alternatives excluding the recommended one
    alternatives = []
    for lang_code, info in all_languages.items():
        if lang_code != recommended_lang:
            alternatives.append({
                'code': lang_code,
                'name': info['name'],
                'popularity': info['popularity'],
                'difficulty': info['difficulty'],
                'use_case': info['use_case'],
                'matches_repo': primary_lang and isinstance(primary_lang, str) and lang_code == primary_lang.replace('c#', 'csharp')
            })

    # Sort by popularity and repo match
    alternatives.sort(key=lambda x: (not x['matches_repo'], x['popularity'] != 'Very High', x['popularity'] != 'High'))

    return alternatives[:4]  # Return top 4 alternatives