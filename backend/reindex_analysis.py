#!/usr/bin/env python3
"""
Re-index existing analysis to fix indexing status
"""
import sys
import os
sys.path.append('/app')

import asyncio
from app.database import SessionLocal
from app.models import RepoAnalysis
from app.services.indexing_service import IndexingService

async def reindex_analysis(analysis_id: int):
    """Re-index an existing analysis"""
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            print(f"Analysis {analysis_id} not found")
            return
        
        print(f"Re-indexing analysis {analysis_id}: {analysis.repo_owner}/{analysis.repo_name}")
        print(f"Current indexing status: {analysis.indexing_status}")
        
        # Get analysis results to extract repository content
        if not analysis.analysis_results:
            print("No analysis results found - cannot re-index")
            return
        
        # Create mock repository content from analysis results
        repo_content = {
            "code_samples": {},  # We'll use a minimal set for testing
            "repository_info": {
                "name": analysis.repo_name,
                "owner": analysis.repo_owner
            }
        }
        
        # Add some sample content for indexing
        repo_content["code_samples"]["README.md"] = "# " + analysis.repo_name
        repo_content["code_samples"]["main.py"] = "# Main application file"
        
        # Trigger re-indexing
        indexing_service = IndexingService()
        result = await indexing_service.trigger_reindex(analysis_id, repo_content)
        
        print(f"Re-indexing completed: {result}")
        
        # Check updated status
        db.refresh(analysis)
        print(f"New indexing status: {analysis.indexing_status}")
        print(f"Last indexed at: {analysis.last_indexed_at}")
        
    except Exception as e:
        print(f"Error re-indexing analysis: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(reindex_analysis(1))
